<?php
/**
 * Taxi Booking Form Processing
 * This file handles advanced form processing, data storage, and email notifications
 */

class TaxiBookingProcessor {
    private $bookings_file = 'bookings.json';
    private $errors = [];

    public function __construct() {
        // Ensure bookings file exists
        if (!file_exists($this->bookings_file)) {
            file_put_contents($this->bookings_file, '');
        }
    }

    /**
     * Process the booking form submission
     */
    public function processBooking($data) {
        // Validate the data
        if (!$this->validateBookingData($data)) {
            return false;
        }

        // Generate booking ID
        $booking_id = $this->generateBookingId();

        // Prepare booking data
        $booking = [
            'booking_id' => $booking_id,
            'full_name' => $data['full_name'],
            'email' => $data['email'],
            'mobile' => $data['mobile'],
            'duty_type' => $data['duty_type'],
            'vehicle_type' => $data['vehicle_type'],
            'pickup_location' => $data['pickup_location'],
            'drop_location' => $data['drop_location'],
            'select_date' => $data['select_date'],
            'select_time' => $data['select_time'],
            'special_requirements' => $data['special_requirements'],
            'booking_time' => date('Y-m-d H:i:s'),
            'status' => 'pending'
        ];

        // Save booking
        if ($this->saveBooking($booking)) {
            // Send confirmation email (if email functionality is available)
            $this->sendConfirmationEmail($booking);
            return $booking_id;
        }

        return false;
    }

    /**
     * Validate booking data
     */
    private function validateBookingData($data) {
        $this->errors = [];

        // Required fields validation
        $required_fields = [
            'full_name' => 'Full name',
            'email' => 'Email address',
            'mobile' => 'Mobile number',
            'duty_type' => 'Duty type',
            'vehicle_type' => 'Vehicle type',
            'pickup_location' => 'Pickup location',
            'drop_location' => 'Drop location',
            'select_date' => 'Date',
            'select_time' => 'Time'
        ];

        foreach ($required_fields as $field => $label) {
            if (empty($data[$field])) {
                $this->errors[$field] = $label . ' is required';
            }
        }

        // Email validation
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->errors['email'] = 'Please enter a valid email address';
        }

        // Mobile number validation
        if (!empty($data['mobile']) && !preg_match('/^[0-9]{10}$/', $data['mobile'])) {
            $this->errors['mobile'] = 'Please enter a valid 10-digit mobile number';
        }

        // Date validation (should not be in the past)
        if (!empty($data['select_date'])) {
            $selected_date = strtotime($data['select_date']);
            $today = strtotime(date('Y-m-d'));
            if ($selected_date < $today) {
                $this->errors['select_date'] = 'Please select a future date';
            }
        }

        return empty($this->errors);
    }

    /**
     * Generate unique booking ID
     */
    private function generateBookingId() {
        return 'TXI' . date('Ymd') . rand(1000, 9999);
    }

    /**
     * Save booking to file
     */
    private function saveBooking($booking) {
        $bookings = $this->getAllBookings();
        $bookings[] = $booking;

        return file_put_contents($this->bookings_file, json_encode($bookings, JSON_PRETTY_PRINT)) !== false;
    }

    /**
     * Get all bookings
     */
    public function getAllBookings() {
        if (!file_exists($this->bookings_file) || filesize($this->bookings_file) == 0) {
            return [];
        }

        $content = file_get_contents($this->bookings_file);
        $bookings = json_decode($content, true);

        return is_array($bookings) ? $bookings : [];
    }

    /**
     * Get booking by ID
     */
    public function getBookingById($booking_id) {
        $bookings = $this->getAllBookings();

        foreach ($bookings as $booking) {
            if ($booking['booking_id'] === $booking_id) {
                return $booking;
            }
        }

        return null;
    }

    /**
     * Send confirmation email (basic implementation)
     */
    private function sendConfirmationEmail($booking) {
        $to = $booking['email'];
        $subject = 'Taxi Booking Confirmation - ' . $booking['booking_id'];

        $message = "
        <html>
        <head>
            <title>Booking Confirmation</title>
        </head>
        <body>
            <h2>Taxi Booking Confirmation</h2>
            <p>Dear {$booking['full_name']},</p>
            <p>Your taxi booking has been confirmed. Here are the details:</p>

            <table border='1' cellpadding='10' cellspacing='0'>
                <tr><td><strong>Booking ID:</strong></td><td>{$booking['booking_id']}</td></tr>
                <tr><td><strong>Date:</strong></td><td>{$booking['select_date']}</td></tr>
                <tr><td><strong>Time:</strong></td><td>{$booking['select_time']}</td></tr>
                <tr><td><strong>Pickup:</strong></td><td>{$booking['pickup_location']}</td></tr>
                <tr><td><strong>Drop:</strong></td><td>{$booking['drop_location']}</td></tr>
                <tr><td><strong>Vehicle Type:</strong></td><td>{$booking['vehicle_type']}</td></tr>
                <tr><td><strong>Duty Type:</strong></td><td>{$booking['duty_type']}</td></tr>
            </table>

            <p>We will contact you shortly to confirm the details.</p>
            <p>Thank you for choosing our service!</p>
        </body>
        </html>
        ";

        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= 'From: <EMAIL>' . "\r\n";

        // Note: mail() function requires proper server configuration
        // For production, consider using PHPMailer or similar library
        @mail($to, $subject, $message, $headers);
    }

    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }

    /**
     * Get booking statistics
     */
    public function getBookingStats() {
        $bookings = $this->getAllBookings();
        $stats = [
            'total_bookings' => count($bookings),
            'pending_bookings' => 0,
            'completed_bookings' => 0,
            'popular_vehicle_types' => [],
            'popular_duty_types' => []
        ];

        foreach ($bookings as $booking) {
            // Count by status
            if ($booking['status'] === 'pending') {
                $stats['pending_bookings']++;
            } elseif ($booking['status'] === 'completed') {
                $stats['completed_bookings']++;
            }

            // Count vehicle types
            $vehicle = $booking['vehicle_type'];
            $stats['popular_vehicle_types'][$vehicle] = ($stats['popular_vehicle_types'][$vehicle] ?? 0) + 1;

            // Count duty types
            $duty = $booking['duty_type'];
            $stats['popular_duty_types'][$duty] = ($stats['popular_duty_types'][$duty] ?? 0) + 1;
        }

        return $stats;
    }
}

// Usage example (uncomment to test):
/*
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $processor = new TaxiBookingProcessor();
    $booking_id = $processor->processBooking($_POST);

    if ($booking_id) {
        echo "Booking successful! Your booking ID is: " . $booking_id;
    } else {
        echo "Booking failed. Errors: " . implode(', ', $processor->getErrors());
    }
}
*/
?>