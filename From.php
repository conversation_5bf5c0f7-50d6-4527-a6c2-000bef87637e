<?php
/**
 * Taxi Booking Form Processing
 * This file handles advanced form processing, data storage, and email notifications
 */

class TaxiBookingProcessor {
    private $bookings_file = 'bookings.json';
    private $errors = [];

    public function __construct() {
        // Ensure bookings file exists
        if (!file_exists($this->bookings_file)) {
            file_put_contents($this->bookings_file, '');
        }
    }

    /**
     * Process the booking form submission
     */
    public function processBooking($data) {
        // Validate the data
        if (!$this->validateBookingData($data)) {
            return false;
        }

        // Generate booking ID
        $booking_id = $this->generateBookingId();

        // Prepare booking data
        $booking = [
            'booking_id' => $booking_id,
            'full_name' => $data['full_name'],
            'email' => $data['email'],
            'mobile' => $data['mobile'],
            'duty_type' => $data['duty_type'],
            'vehicle_type' => $data['vehicle_type'],
            'pickup_location' => $data['pickup_location'],
            'drop_location' => $data['drop_location'],
            'select_date' => $data['select_date'],
            'select_time' => $data['select_time'],
            'special_requirements' => $data['special_requirements'],
            'booking_time' => date('Y-m-d H:i:s'),
            'status' => 'pending'
        ];

        // Save booking
        if ($this->saveBooking($booking)) {
            // Send confirmation email (if email functionality is available)
            $this->sendConfirmationEmail($booking);
            return $booking_id;
        }

        return false;
    }

    /**
     * Validate booking data
     */
    private function validateBookingData($data) {
        $this->errors = [];

        // Required fields validation
        $required_fields = [
            'full_name' => 'Full name',
            'email' => 'Email address',
            'mobile' => 'Mobile number',
            'duty_type' => 'Duty type',
            'vehicle_type' => 'Vehicle type',
            'pickup_location' => 'Pickup location',
            'drop_location' => 'Drop location',
            'select_date' => 'Date',
            'select_time' => 'Time'
        ];

        foreach ($required_fields as $field => $label) {
            if (empty($data[$field])) {
                $this->errors[$field] = $label . ' is required';
            }
        }

        // Email validation
        if (!empty($data['email']) && !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->errors['email'] = 'Please enter a valid email address';
        }

        // Mobile number validation
        if (!empty($data['mobile']) && !preg_match('/^[0-9]{10}$/', $data['mobile'])) {
            $this->errors['mobile'] = 'Please enter a valid 10-digit mobile number';
        }

        // Date validation (should not be in the past)
        if (!empty($data['select_date'])) {
            $selected_date = strtotime($data['select_date']);
            $today = strtotime(date('Y-m-d'));
            if ($selected_date < $today) {
                $this->errors['select_date'] = 'Please select a future date';
            }
        }

        return empty($this->errors);
    }

    /**
     * Generate unique booking ID
     */
    private function generateBookingId() {
        return 'TXI' . date('Ymd') . rand(1000, 9999);
    }

    /**
     * Save booking to file
     */
    private function saveBooking($booking) {
        $bookings = $this->getAllBookings();
        $bookings[] = $booking;

        return file_put_contents($this->bookings_file, json_encode($bookings, JSON_PRETTY_PRINT)) !== false;
    }

    /**
     * Get all bookings
     */
    public function getAllBookings() {
        if (!file_exists($this->bookings_file) || filesize($this->bookings_file) == 0) {
            return [];
        }

        $content = file_get_contents($this->bookings_file);
        $bookings = json_decode($content, true);

        return is_array($bookings) ? $bookings : [];
    }

    /**
     * Get booking by ID
     */
    public function getBookingById($booking_id) {
        $bookings = $this->getAllBookings();

        foreach ($bookings as $booking) {
            if ($booking['booking_id'] === $booking_id) {
                return $booking;
            }
        }

        return null;
    }

    /**
     * Send confirmation email (basic implementation)
     */
    private function sendConfirmationEmail($booking) {
        // Send confirmation to customer
        $this->sendCustomerConfirmation($booking);

        // Send booking notification to admin
        $this->sendAdminNotification($booking);
    }

    /**
     * Send confirmation email to customer
     */
    private function sendCustomerConfirmation($booking) {
        $to = $booking['email'];
        $subject = 'Taxi Booking Confirmation - ' . $booking['booking_id'];

        $message = "
        <html>
        <head>
            <title>Booking Confirmation</title>
            <style>
                body { font-family: Arial, sans-serif; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #ff8c00; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background: #ff8c00; color: white; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>Taxi Booking Confirmation</h2>
                </div>
                <div class='content'>
                    <p>Dear {$booking['full_name']},</p>
                    <p>Your taxi booking has been confirmed. Here are the details:</p>

                    <table>
                        <tr><th>Booking ID</th><td>{$booking['booking_id']}</td></tr>
                        <tr><th>Date</th><td>{$booking['select_date']}</td></tr>
                        <tr><th>Time</th><td>{$booking['select_time']}</td></tr>
                        <tr><th>Pickup Location</th><td>{$booking['pickup_location']}</td></tr>
                        <tr><th>Drop Location</th><td>{$booking['drop_location']}</td></tr>
                        <tr><th>Vehicle Type</th><td>" . ucfirst($booking['vehicle_type']) . "</td></tr>
                        <tr><th>Duty Type</th><td>" . ucfirst($booking['duty_type']) . "</td></tr>
                        <tr><th>Mobile Number</th><td>{$booking['mobile']}</td></tr>
                        <tr><th>Special Requirements</th><td>" . ($booking['special_requirements'] ?: 'None') . "</td></tr>
                    </table>

                    <p><strong>We will contact you shortly to confirm the details.</strong></p>
                    <p>Thank you for choosing our taxi service!</p>
                </div>
            </div>
        </body>
        </html>
        ";

        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= 'From: <EMAIL>' . "\r\n";

        // Send email to customer
        @mail($to, $subject, $message, $headers);
    }

    /**
     * Send booking notification to admin
     */
    private function sendAdminNotification($booking) {
        $admin_email = '<EMAIL>';
        $subject = 'New Taxi Booking Received - ' . $booking['booking_id'];

        $message = "
        <html>
        <head>
            <title>New Booking Notification</title>
            <style>
                body { font-family: Arial, sans-serif; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #ff8c00; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
                th { background: #ff8c00; color: white; }
                .urgent { background: #ffebee; border-left: 4px solid #f44336; padding: 15px; margin: 20px 0; }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h2>🚗 New Taxi Booking Received</h2>
                </div>
                <div class='content'>
                    <div class='urgent'>
                        <strong>⚠️ Action Required:</strong> A new taxi booking has been received and requires your attention.
                    </div>

                    <h3>Customer Details:</h3>
                    <table>
                        <tr><th>Booking ID</th><td><strong>{$booking['booking_id']}</strong></td></tr>
                        <tr><th>Customer Name</th><td>{$booking['full_name']}</td></tr>
                        <tr><th>Email</th><td>{$booking['email']}</td></tr>
                        <tr><th>Mobile Number</th><td><strong>{$booking['mobile']}</strong></td></tr>
                        <tr><th>Booking Time</th><td>{$booking['booking_time']}</td></tr>
                    </table>

                    <h3>Trip Details:</h3>
                    <table>
                        <tr><th>Date</th><td><strong>{$booking['select_date']}</strong></td></tr>
                        <tr><th>Time</th><td><strong>{$booking['select_time']}</strong></td></tr>
                        <tr><th>Pickup Location</th><td>{$booking['pickup_location']}</td></tr>
                        <tr><th>Drop Location</th><td>{$booking['drop_location']}</td></tr>
                        <tr><th>Vehicle Type</th><td>" . ucfirst($booking['vehicle_type']) . "</td></tr>
                        <tr><th>Duty Type</th><td>" . ucfirst($booking['duty_type']) . "</td></tr>
                    </table>

                    <h3>Additional Information:</h3>
                    <table>
                        <tr><th>Special Requirements</th><td>" . ($booking['special_requirements'] ?: 'None specified') . "</td></tr>
                        <tr><th>Status</th><td><span style='background: #fff3cd; padding: 5px 10px; border-radius: 3px;'>Pending</span></td></tr>
                    </table>

                    <div style='background: #e8f5e8; border: 1px solid #4caf50; padding: 15px; margin: 20px 0; border-radius: 5px;'>
                        <strong>Next Steps:</strong>
                        <ul>
                            <li>Contact customer at {$booking['mobile']} to confirm booking</li>
                            <li>Assign appropriate vehicle and driver</li>
                            <li>Send final confirmation to customer</li>
                        </ul>
                    </div>
                </div>
            </div>
        </body>
        </html>
        ";

        $headers = "MIME-Version: 1.0" . "\r\n";
        $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
        $headers .= 'From: <EMAIL>' . "\r\n";
        $headers .= 'Reply-To: ' . $booking['email'] . "\r\n";

        // Send email to admin
        @mail($admin_email, $subject, $message, $headers);
    }

    /**
     * Get validation errors
     */
    public function getErrors() {
        return $this->errors;
    }

    /**
     * Get booking statistics
     */
    public function getBookingStats() {
        $bookings = $this->getAllBookings();
        $stats = [
            'total_bookings' => count($bookings),
            'pending_bookings' => 0,
            'completed_bookings' => 0,
            'popular_vehicle_types' => [],
            'popular_duty_types' => []
        ];

        foreach ($bookings as $booking) {
            // Count by status
            if ($booking['status'] === 'pending') {
                $stats['pending_bookings']++;
            } elseif ($booking['status'] === 'completed') {
                $stats['completed_bookings']++;
            }

            // Count vehicle types
            $vehicle = $booking['vehicle_type'];
            $stats['popular_vehicle_types'][$vehicle] = ($stats['popular_vehicle_types'][$vehicle] ?? 0) + 1;

            // Count duty types
            $duty = $booking['duty_type'];
            $stats['popular_duty_types'][$duty] = ($stats['popular_duty_types'][$duty] ?? 0) + 1;
        }

        return $stats;
    }
}

// Usage example (uncomment to test):
/*
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $processor = new TaxiBookingProcessor();
    $booking_id = $processor->processBooking($_POST);

    if ($booking_id) {
        echo "Booking successful! Your booking ID is: " . $booking_id;
    } else {
        echo "Booking failed. Errors: " . implode(', ', $processor->getErrors());
    }
}
*/
?>