<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Your Taxi Ride</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(45, 45, 45, 0.95);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 1000px;
            width: 100%;
        }

        .form-title {
            color: #ffffff;
            font-size: 2.5rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            position: relative;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 15px 20px;
            background: rgba(60, 60, 60, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #ff8c00;
            background: rgba(70, 70, 70, 0.9);
            box-shadow: 0 0 10px rgba(255, 140, 0, 0.3);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-select {
            cursor: pointer;
        }

        .form-select option {
            background: #3c3c3c;
            color: #ffffff;
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #ff8c00;
            font-size: 18px;
            pointer-events: none;
        }

        .submit-btn {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
            color: #ffffff;
            border: none;
            padding: 18px 40px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #ff6b00 0%, #ff4500 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 140, 0, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }

            .form-title {
                font-size: 2rem;
            }

            .container {
                padding: 20px;
            }
        }

        .error {
            color: #ff4444;
            font-size: 14px;
            margin-top: 5px;
        }

        .success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid #4caf50;
            color: #4caf50;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
    </style>
</head>
<body>
    <?php
    $success_message = '';
    $errors = [];

    if ($_SERVER['REQUEST_METHOD'] == 'POST') {
        // Sanitize and validate form data
        $full_name = trim($_POST['full_name'] ?? '');
        $email = trim($_POST['email'] ?? '');
        $mobile = trim($_POST['mobile'] ?? '');
        $duty_type = $_POST['duty_type'] ?? '';
        $vehicle_type = $_POST['vehicle_type'] ?? '';
        $pickup_location = trim($_POST['pickup_location'] ?? '');
        $drop_location = trim($_POST['drop_location'] ?? '');
        $select_date = $_POST['select_date'] ?? '';
        $select_time = $_POST['select_time'] ?? '';
        $special_requirements = trim($_POST['special_requirements'] ?? '');

        // Validation
        if (empty($full_name)) {
            $errors['full_name'] = 'Full name is required';
        }

        if (empty($email)) {
            $errors['email'] = 'Email address is required';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $errors['email'] = 'Please enter a valid email address';
        }

        if (empty($mobile)) {
            $errors['mobile'] = 'Mobile number is required';
        } elseif (!preg_match('/^[0-9]{10}$/', $mobile)) {
            $errors['mobile'] = 'Please enter a valid 10-digit mobile number';
        }

        if (empty($duty_type)) {
            $errors['duty_type'] = 'Please select duty type';
        }

        if (empty($vehicle_type)) {
            $errors['vehicle_type'] = 'Please select vehicle type';
        }

        if (empty($pickup_location)) {
            $errors['pickup_location'] = 'Pickup location is required';
        }

        if (empty($drop_location)) {
            $errors['drop_location'] = 'Drop location is required';
        }

        if (empty($select_date)) {
            $errors['select_date'] = 'Please select date';
        }

        if (empty($select_time)) {
            $errors['select_time'] = 'Please select time';
        }

        // If no errors, process the booking
        if (empty($errors)) {
            // Here you can save to database, send email, etc.
            // For now, we'll just show a success message
            $success_message = 'Your taxi booking has been confirmed! We will contact you shortly.';

            // You can also save to a file or database here
            $booking_data = [
                'full_name' => $full_name,
                'email' => $email,
                'mobile' => $mobile,
                'duty_type' => $duty_type,
                'vehicle_type' => $vehicle_type,
                'pickup_location' => $pickup_location,
                'drop_location' => $drop_location,
                'select_date' => $select_date,
                'select_time' => $select_time,
                'special_requirements' => $special_requirements,
                'booking_time' => date('Y-m-d H:i:s')
            ];

            // Save to file (you can replace this with database storage)
            file_put_contents('bookings.json', json_encode($booking_data) . "\n", FILE_APPEND | LOCK_EX);
        }
    }
    ?>

    <div class="container">
        <h1 class="form-title">Book Your Taxi Ride</h1>

        <?php if ($success_message): ?>
            <div class="success"><?php echo htmlspecialchars($success_message); ?></div>
        <?php endif; ?>

        <form method="POST" action="">
            <div class="form-grid">
                <!-- Full Name -->
                <div class="form-group">
                    <input type="text" name="full_name" class="form-input" placeholder="Your Full Name"
                           value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
                    <span class="icon">👤</span>
                    <?php if (isset($errors['full_name'])): ?>
                        <div class="error"><?php echo $errors['full_name']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Email Address -->
                <div class="form-group">
                    <input type="email" name="email" class="form-input" placeholder="Email Address"
                           value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>" required>
                    <span class="icon">📧</span>
                    <?php if (isset($errors['email'])): ?>
                        <div class="error"><?php echo $errors['email']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Mobile Number -->
                <div class="form-group">
                    <input type="tel" name="mobile" class="form-input" placeholder="Mobile Number"
                           value="<?php echo htmlspecialchars($_POST['mobile'] ?? ''); ?>" required>
                    <span class="icon">📱</span>
                    <?php if (isset($errors['mobile'])): ?>
                        <div class="error"><?php echo $errors['mobile']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Duty Type -->
                <div class="form-group">
                    <select name="duty_type" class="form-select" required>
                        <option value="">-- Select Duty Type --</option>
                        <option value="local" <?php echo ($_POST['duty_type'] ?? '') == 'local' ? 'selected' : ''; ?>>Local</option>
                        <option value="outstation" <?php echo ($_POST['duty_type'] ?? '') == 'outstation' ? 'selected' : ''; ?>>Outstation</option>
                        <option value="airport" <?php echo ($_POST['duty_type'] ?? '') == 'airport' ? 'selected' : ''; ?>>Airport Transfer</option>
                        <option value="hourly" <?php echo ($_POST['duty_type'] ?? '') == 'hourly' ? 'selected' : ''; ?>>Hourly Rental</option>
                    </select>
                    <span class="icon">🚗</span>
                    <?php if (isset($errors['duty_type'])): ?>
                        <div class="error"><?php echo $errors['duty_type']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Vehicle Type -->
                <div class="form-group">
                    <select name="vehicle_type" class="form-select" required>
                        <option value="">-- Select Vehicle Type --</option>
                        <option value="hatchback" <?php echo ($_POST['vehicle_type'] ?? '') == 'hatchback' ? 'selected' : ''; ?>>Hatchback</option>
                        <option value="sedan" <?php echo ($_POST['vehicle_type'] ?? '') == 'sedan' ? 'selected' : ''; ?>>Sedan</option>
                        <option value="suv" <?php echo ($_POST['vehicle_type'] ?? '') == 'suv' ? 'selected' : ''; ?>>SUV</option>
                        <option value="luxury" <?php echo ($_POST['vehicle_type'] ?? '') == 'luxury' ? 'selected' : ''; ?>>Luxury Car</option>
                        <option value="tempo" <?php echo ($_POST['vehicle_type'] ?? '') == 'tempo' ? 'selected' : ''; ?>>Tempo Traveller</option>
                    </select>
                    <span class="icon">🚙</span>
                    <?php if (isset($errors['vehicle_type'])): ?>
                        <div class="error"><?php echo $errors['vehicle_type']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Pickup Location -->
                <div class="form-group">
                    <input type="text" name="pickup_location" class="form-input" placeholder="Pickup Location"
                           value="<?php echo htmlspecialchars($_POST['pickup_location'] ?? ''); ?>" required>
                    <span class="icon">📍</span>
                    <?php if (isset($errors['pickup_location'])): ?>
                        <div class="error"><?php echo $errors['pickup_location']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Drop Location -->
                <div class="form-group">
                    <input type="text" name="drop_location" class="form-input" placeholder="Drop Location"
                           value="<?php echo htmlspecialchars($_POST['drop_location'] ?? ''); ?>" required>
                    <span class="icon">📍</span>
                    <?php if (isset($errors['drop_location'])): ?>
                        <div class="error"><?php echo $errors['drop_location']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Select Date -->
                <div class="form-group">
                    <input type="date" name="select_date" class="form-input"
                           value="<?php echo htmlspecialchars($_POST['select_date'] ?? ''); ?>"
                           min="<?php echo date('Y-m-d'); ?>" required>
                    <span class="icon">📅</span>
                    <?php if (isset($errors['select_date'])): ?>
                        <div class="error"><?php echo $errors['select_date']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Select Time -->
                <div class="form-group">
                    <input type="time" name="select_time" class="form-input"
                           value="<?php echo htmlspecialchars($_POST['select_time'] ?? ''); ?>" required>
                    <span class="icon">🕐</span>
                    <?php if (isset($errors['select_time'])): ?>
                        <div class="error"><?php echo $errors['select_time']; ?></div>
                    <?php endif; ?>
                </div>

                <!-- Special Requirements -->
                <div class="form-group full-width">
                    <textarea name="special_requirements" class="form-textarea"
                              placeholder="Special Requirements: Eg - Drinking Water, Clean Car, etc."><?php echo htmlspecialchars($_POST['special_requirements'] ?? ''); ?></textarea>
                    <span class="icon">📝</span>
                </div>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="submit-btn">Confirm Booking</button>
        </form>
    </div>

    <script>
        // Add some interactive features
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-focus first input
            const firstInput = document.querySelector('.form-input');
            if (firstInput) {
                firstInput.focus();
            }

            // Add animation to form groups
            const formGroups = document.querySelectorAll('.form-group');
            formGroups.forEach((group, index) => {
                group.style.animationDelay = `${index * 0.1}s`;
                group.style.animation = 'fadeInUp 0.6s ease forwards';
            });

            // Mobile number validation
            const mobileInput = document.querySelector('input[name="mobile"]');
            if (mobileInput) {
                mobileInput.addEventListener('input', function() {
                    this.value = this.value.replace(/[^0-9]/g, '').slice(0, 10);
                });
            }
        });

        // Add CSS animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .form-group {
                opacity: 0;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>