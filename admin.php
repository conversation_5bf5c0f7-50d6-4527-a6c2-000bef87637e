<?php
require_once 'From.php';

$processor = new TaxiBookingProcessor();
$bookings = $processor->getAllBookings();
$stats = $processor->getBookingStats();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Taxi Booking Admin Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
            min-height: 100vh;
            color: #ffffff;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5rem;
            color: #ff8c00;
            margin-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .stat-card {
            background: rgba(45, 45, 45, 0.95);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid rgba(255, 140, 0, 0.3);
        }

        .stat-card h3 {
            color: #ff8c00;
            font-size: 2rem;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #cccccc;
            font-size: 1.1rem;
        }

        .bookings-table {
            background: rgba(45, 45, 45, 0.95);
            border-radius: 10px;
            padding: 20px;
            overflow-x: auto;
        }

        .bookings-table h2 {
            color: #ff8c00;
            margin-bottom: 20px;
            text-align: center;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        th {
            background: rgba(255, 140, 0, 0.2);
            color: #ff8c00;
            font-weight: bold;
        }

        tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .status.pending {
            background: rgba(255, 193, 7, 0.2);
            color: #ffc107;
        }

        .status.completed {
            background: rgba(76, 175, 80, 0.2);
            color: #4caf50;
        }

        .no-bookings {
            text-align: center;
            color: #cccccc;
            font-style: italic;
            padding: 40px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            table {
                font-size: 0.9rem;
            }
            
            th, td {
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Taxi Booking Dashboard</h1>
            <p>Manage and view all taxi bookings</p>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <h3><?php echo $stats['total_bookings']; ?></h3>
                <p>Total Bookings</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['pending_bookings']; ?></h3>
                <p>Pending Bookings</p>
            </div>
            <div class="stat-card">
                <h3><?php echo $stats['completed_bookings']; ?></h3>
                <p>Completed Bookings</p>
            </div>
            <div class="stat-card">
                <h3><?php echo count($stats['popular_vehicle_types']); ?></h3>
                <p>Vehicle Types Used</p>
            </div>
        </div>

        <!-- Bookings Table -->
        <div class="bookings-table">
            <h2>Recent Bookings</h2>
            
            <?php if (empty($bookings)): ?>
                <div class="no-bookings">
                    <p>No bookings found. The booking form is ready to accept new reservations!</p>
                </div>
            <?php else: ?>
                <table>
                    <thead>
                        <tr>
                            <th>Booking ID</th>
                            <th>Customer Name</th>
                            <th>Mobile</th>
                            <th>Date & Time</th>
                            <th>Pickup</th>
                            <th>Drop</th>
                            <th>Vehicle</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach (array_reverse($bookings) as $booking): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($booking['booking_id']); ?></td>
                                <td><?php echo htmlspecialchars($booking['full_name']); ?></td>
                                <td><?php echo htmlspecialchars($booking['mobile']); ?></td>
                                <td>
                                    <?php echo htmlspecialchars($booking['select_date']); ?><br>
                                    <small><?php echo htmlspecialchars($booking['select_time']); ?></small>
                                </td>
                                <td><?php echo htmlspecialchars($booking['pickup_location']); ?></td>
                                <td><?php echo htmlspecialchars($booking['drop_location']); ?></td>
                                <td><?php echo htmlspecialchars(ucfirst($booking['vehicle_type'])); ?></td>
                                <td>
                                    <span class="status <?php echo $booking['status']; ?>">
                                        <?php echo ucfirst($booking['status']); ?>
                                    </span>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
