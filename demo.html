<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Your Taxi Ride - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #2c2c2c 0%, #1a1a1a 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(45, 45, 45, 0.95);
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            max-width: 1000px;
            width: 100%;
        }

        .form-title {
            color: #ffffff;
            font-size: 2.5rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .form-group {
            position: relative;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 15px 20px;
            background: rgba(60, 60, 60, 0.8);
            border: 2px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: #ff8c00;
            background: rgba(70, 70, 70, 0.9);
            box-shadow: 0 0 10px rgba(255, 140, 0, 0.3);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-select {
            cursor: pointer;
        }

        .form-select option {
            background: #3c3c3c;
            color: #ffffff;
        }

        .form-textarea {
            resize: vertical;
            min-height: 100px;
        }

        .icon {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            color: #ff8c00;
            font-size: 18px;
            pointer-events: none;
        }

        .submit-btn {
            background: linear-gradient(135deg, #ff8c00 0%, #ff6b00 100%);
            color: #ffffff;
            border: none;
            padding: 18px 40px;
            font-size: 18px;
            font-weight: bold;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .submit-btn:hover {
            background: linear-gradient(135deg, #ff6b00 0%, #ff4500 100%);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 140, 0, 0.3);
        }

        .submit-btn:active {
            transform: translateY(0);
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .form-title {
                font-size: 2rem;
            }
            
            .container {
                padding: 20px;
            }
        }

        .success {
            background: rgba(76, 175, 80, 0.1);
            border: 1px solid #4caf50;
            color: #4caf50;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
            display: none;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .form-group {
            opacity: 0;
            animation: fadeInUp 0.6s ease forwards;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="form-title">Book Your Taxi Ride</h1>
        
        <div id="success-message" class="success">
            Your taxi booking has been submitted! We will contact you shortly.
        </div>
        
        <form id="bookingForm">
            <div class="form-grid">
                <!-- Full Name -->
                <div class="form-group" style="animation-delay: 0.1s;">
                    <input type="text" name="full_name" class="form-input" placeholder="Your Full Name" required>
                    <span class="icon">👤</span>
                </div>
                
                <!-- Email Address -->
                <div class="form-group" style="animation-delay: 0.2s;">
                    <input type="email" name="email" class="form-input" placeholder="Email Address" required>
                    <span class="icon">📧</span>
                </div>
                
                <!-- Mobile Number -->
                <div class="form-group" style="animation-delay: 0.3s;">
                    <input type="tel" name="mobile" class="form-input" placeholder="Mobile Number" required>
                    <span class="icon">📱</span>
                </div>
                
                <!-- Duty Type -->
                <div class="form-group" style="animation-delay: 0.4s;">
                    <select name="duty_type" class="form-select" required>
                        <option value="">-- Select Duty Type --</option>
                        <option value="local">Local</option>
                        <option value="outstation">Outstation</option>
                        <option value="airport">Airport Transfer</option>
                        <option value="hourly">Hourly Rental</option>
                    </select>
                    <span class="icon">🚗</span>
                </div>
                
                <!-- Vehicle Type -->
                <div class="form-group" style="animation-delay: 0.5s;">
                    <select name="vehicle_type" class="form-select" required>
                        <option value="">-- Select Vehicle Type --</option>
                        <option value="hatchback">Hatchback</option>
                        <option value="sedan">Sedan</option>
                        <option value="suv">SUV</option>
                        <option value="luxury">Luxury Car</option>
                        <option value="tempo">Tempo Traveller</option>
                    </select>
                    <span class="icon">🚙</span>
                </div>
                
                <!-- Pickup Location -->
                <div class="form-group" style="animation-delay: 0.6s;">
                    <input type="text" name="pickup_location" class="form-input" placeholder="Pickup Location" required>
                    <span class="icon">📍</span>
                </div>
                
                <!-- Drop Location -->
                <div class="form-group" style="animation-delay: 0.7s;">
                    <input type="text" name="drop_location" class="form-input" placeholder="Drop Location" required>
                    <span class="icon">📍</span>
                </div>
                
                <!-- Select Date -->
                <div class="form-group" style="animation-delay: 0.8s;">
                    <input type="date" name="select_date" class="form-input" required>
                    <span class="icon">📅</span>
                </div>
                
                <!-- Select Time -->
                <div class="form-group" style="animation-delay: 0.9s;">
                    <input type="time" name="select_time" class="form-input" required>
                    <span class="icon">🕐</span>
                </div>
                
                <!-- Special Requirements -->
                <div class="form-group full-width" style="animation-delay: 1.0s;">
                    <textarea name="special_requirements" class="form-textarea" 
                              placeholder="Special Requirements: Eg - Drinking Water, Clean Car, etc."></textarea>
                    <span class="icon">📝</span>
                </div>
            </div>
            
            <!-- Submit Button -->
            <button type="submit" class="submit-btn">Confirm Booking</button>
        </form>
    </div>

    <script>
        // Set minimum date to today
        document.querySelector('input[name="select_date"]').min = new Date().toISOString().split('T')[0];
        
        // Mobile number validation
        const mobileInput = document.querySelector('input[name="mobile"]');
        mobileInput.addEventListener('input', function() {
            this.value = this.value.replace(/[^0-9]/g, '').slice(0, 10);
        });
        
        // Form submission
        document.getElementById('bookingForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Show success message
            document.getElementById('success-message').style.display = 'block';
            
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
            
            // Reset form after 3 seconds
            setTimeout(() => {
                this.reset();
                document.getElementById('success-message').style.display = 'none';
            }, 3000);
        });
        
        // Auto-focus first input
        document.querySelector('.form-input').focus();
    </script>
</body>
</html>
